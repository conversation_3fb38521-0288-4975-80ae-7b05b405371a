# ✅ Correction définitive du plan large (V2)

## 🚨 **Problème identifié dans le log**

Analyse de la séquence problématique :
```
[13:05:03,129] All cameras hidden (plan large)
[13:05:03,718] Camera activated: CAM 2     // Seulement 589ms plus tard ! ❌
```

**Problème** : Quand on passe au plan large, la réactivation d'un micro se fait quasi-instantanément au lieu de respecter le délai de 4000ms.

## 🔍 **Analyse de la cause racine**

### **Séquence problématique**
1. **Passage au plan large** :
   ```typescript
   this.cameraState.activeCamera = null;        // ← Caméra = null
   this.cameraState.lastChangeTime = Date.now(); // ← Timestamp mis à jour
   ```

2. **Réactivation d'un micro** :
   ```typescript
   if (this.cameraState.activeCamera === null) {
       delay = this.config.activationDelayMs;  // 100ms seulement !
   }
   ```

3. **Vérification dans `canChangeCamera()`** :
   ```typescript
   // Cette vérification était ignorée car activeCamera = null !
   if (this.cameraState.activeCamera && now - this.cameraState.activatedAt < this.config.cameraMinHoldMs) {
       // Protection jamais appliquée ❌
   }
   ```

### **Le bug**
La fonction `canChangeCamera()` ne vérifiait le délai que si `activeCamera` existait, mais quand on vient du plan large, `activeCamera = null` !

## 🔧 **Correction appliquée**

### **Ajout d'une vérification supplémentaire**
```typescript
private canChangeCamera(targetCamera: string, now: number): { allowed: boolean; retryAfter: number } {
    // Si c'est la même caméra, pas besoin de changer
    if (this.cameraState.activeCamera === targetCamera) {
        return { allowed: false, retryAfter: 0 };
    }

    // Vérifier la durée de maintien minimum de la caméra active
    if (this.cameraState.activeCamera && now - this.cameraState.activatedAt < this.config.cameraMinHoldMs) {
        const remainingHold = this.config.cameraMinHoldMs - (now - this.cameraState.activatedAt);
        return { allowed: false, retryAfter: remainingHold + 100 };
    }

    // 🆕 NOUVEAU : Vérifier le délai minimum depuis le dernier changement (même depuis le plan large)
    if (this.cameraState.lastChangeTime > 0 && now - this.cameraState.lastChangeTime < this.config.cameraMinHoldMs) {
        const remainingInterval = this.config.cameraMinHoldMs - (now - this.cameraState.lastChangeTime);
        if (this.config.debug.verbose) {
            console.log(`Too soon since last change (${remainingInterval}ms remaining, from ${this.cameraState.activeCamera || 'plan large'})`);
        }
        return { allowed: false, retryAfter: remainingInterval + 100 };
    }

    return { allowed: true, retryAfter: 0 };
}
```

## 📊 **Matrice des protections complète**

| Situation | Protection appliquée | Délai | Source |
|-----------|---------------------|-------|--------|
| **Caméra A → Caméra B** | `activatedAt` check | 4000ms | Ligne 398 |
| **Plan large → Caméra** | `lastChangeTime` check | 4000ms | 🆕 Ligne 408 |
| **Plein écran → Caméra** | Délai d'activation | 4000ms | `exitFullscreenMode()` |
| **Première activation** | Aucune protection | 100ms | Cas initial |

## 🎯 **Comportement attendu maintenant**

### **Scénario corrigé**
```
[13:05:03,129] All cameras hidden (plan large)
[13:05:07,129] Camera activated: CAM 2     // 4000ms plus tard ✅
```

### **Logs de debug attendus**
```
[13:05:03,718] [AudioToCameraLink] Too soon since last change (3411ms remaining, from plan large)
[13:05:04,000] [AudioToCameraLink] Scheduling action 'reevaluate' in 3511ms
[13:05:07,511] [AudioToCameraLink] Camera activated: CAM 2
```

## 🔍 **Cas de test pour validation**

### **Test 1 : Plan large → Caméra**
1. Couper tous les micros → Plan large après 4s
2. Réactiver un micro immédiatement
3. ✅ **Vérifier** : Délai de 4s avant activation caméra

### **Test 2 : Caméra → Caméra**
1. Caméra A active
2. Activer micro pour caméra B
3. ✅ **Vérifier** : Délai de 4s avant changement

### **Test 3 : Première activation**
1. Démarrage système (aucune caméra jamais activée)
2. Activer un micro
3. ✅ **Vérifier** : Activation en 100ms

### **Test 4 : Plein écran → Caméra**
1. Mode plein écran actif
2. Une seule personne reste
3. ✅ **Vérifier** : Délai de 4s avant activation caméra

## ✅ **Double protection maintenant**

Le système a maintenant **deux niveaux de protection** :

1. **Protection par caméra active** (`activatedAt`) : Pour les changements caméra → caméra
2. **Protection par dernier changement** (`lastChangeTime`) : Pour tous les autres cas, y compris plan large → caméra

## 🎯 **Configuration finale (inchangée)**

```typescript
export const audioToCameraCustomConfig: AudioToCameraConfig = {
    enabled: true,
    targetScene: 'AUTO CAM',
    audioThresholdDb: -40,
    activationDelayMs: 100,                 // Première activation rapide
    cameraMinHoldMs: 4000,                  // ✅ Protection universelle
    
    micToCameraMapping: [
        { micName: 'mic_invite1', cameraSource: 'CAM 2' },
        { micName: 'mic_invite2_L', cameraSource: 'CAM 3' },
        { micName: 'mic_invite2_R', cameraSource: 'CAM 3' },
    ],
    
    fallbackBehavior: {
        hideAllWhenInactive: true,
    },
    fullscreenMode: {
        enabled: true,
        minActiveMics: 2,
    },
    audioStabilization: {
        enabled: true,
        fluctuationWindowMs: 3000,
        minStableDurationMs: 1000,
    },
    debug: {
        verbose: false,
        logAudioLevels: false,
        logAudioThresholds: true,
        audioLogIntervalMs: 2000,
    },
};
```

## ✅ **Problème définitivement résolu**

Le délai de 4000ms s'applique maintenant **universellement** à tous les changements, y compris depuis le plan large, grâce à la double vérification dans `canChangeCamera()`.
