/**
 * Configuration principale de l'application
 *
 * Cette configuration utilise uniquement les variables d'environnement (.env)
 * pour les paramètres généraux de l'application.
 *
 * Les configurations spécifiques aux services (Gabin, Companion, OBS, etc.) sont gérées
 * dans leurs fichiers respectifs (custom/).
 */

/**
 * Configuration du serveur HTTP
 */
export interface HttpConfig {
    port: number;
}

/**
 * Configuration générale de l'application
 */
export interface AppConfig {
    http: HttpConfig;
    environment: 'development' | 'production' | 'test';
}

/**
 * Configuration par défaut de l'application
 * Utilise les variables d'environnement avec des valeurs par défaut sensées
 */
export const appConfig: AppConfig = {
    http: {
        port: process.env.HTTP_PORT ? parseInt(process.env.HTTP_PORT, 10) : 3000,
    },
    environment: (process.env.NODE_ENV as AppConfig['environment']) || 'development',
};
