# ✅ Simplification finale des timeouts

## 🎯 **Problème identifié**

Nous retombions dans la multiplication des timeouts avec des valeurs similaires :
- `cameraMinHoldMs: 4000`
- `hideDelayMs: 3000`
- `fullscreenToSingleDelayMs: 1500`
- `inactiveToSingleDelayMs: 1500`
- `triggerDelayMs: 3000`
- `minHoldMs: 2000`

## 💡 **Solution : Une seule valeur de référence**

Utilisation de `cameraMinHoldMs: 4000` comme **valeur unique** pour tous les changements de caméra.

## 🔄 **Configuration simplifiée**

### **Avant (6 paramètres de timing)**
```typescript
audioThresholdDb: -40,
activationDelayMs: 100,
cameraMinHoldMs: 4000,
fallbackBehavior: {
    hideDelayMs: 3000,
    fullscreenToSingleDelayMs: 1500,
    inactiveToSingleDelayMs: 1500,
},
fullscreenMode: {
    triggerDelayMs: 3000,
    minHoldMs: 2000,
},
audioStabilization: {
    fluctuationWindowMs: 3000,
    minStableDurationMs: 1000,
},
```

### **Après (4 paramètres de timing)**
```typescript
audioThresholdDb: -40,                     // Seuil déclenchement micros
activationDelayMs: 100,                    // Délai minimal activation
cameraMinHoldMs: 4000,                     // 🎯 VALEUR UNIQUE pour tous les changements
audioStabilization: {
    fluctuationWindowMs: 3000,             // Fenêtre détection fluctuations
    minStableDurationMs: 1000,             // Durée minimum stable
},
```

## 📊 **Utilisation de la valeur unique**

| Situation | Délai utilisé | Justification |
|-----------|---------------|---------------|
| **Plan large → Caméra** | `cameraMinHoldMs` (4000ms) | Évite retours rapides |
| **Plein écran → Caméra** | `cameraMinHoldMs` (4000ms) | Évite retours rapides |
| **Caméra → Caméra** | `cameraMinHoldMs` (4000ms) | Protection anti-changements |
| **Aucun micro → Plan large** | `cameraMinHoldMs` (4000ms) | Évite masquage sur pauses |
| **2+ micros → Plein écran** | `cameraMinHoldMs` (4000ms) | Évite plein écran sur pics |
| **Maintien plein écran** | `cameraMinHoldMs` (4000ms) | Évite sorties rapides |

## 🎯 **Paramètres conservés séparément**

### **Paramètres spécialisés (non liés aux changements de caméra)**
```typescript
activationDelayMs: 100,                    // Délai minimal pour éviter pics audio
audioStabilization: {
    fluctuationWindowMs: 3000,             // Fenêtre de détection des fluctuations
    minStableDurationMs: 1000,             // Durée minimum avant changement stable
},
debug: {
    audioLogIntervalMs: 2000,              // Intervalle des logs audio
}
```

## 📋 **Configuration finale recommandée**

```typescript
export const audioToCameraCustomConfig: AudioToCameraConfig = {
    enabled: true,
    targetScene: 'AUTO CAM',
    
    // Paramètres audio
    audioThresholdDb: -40,                  // Seuil déclenchement micros
    activationDelayMs: 100,                 // Délai minimal activation
    
    // Paramètre unique pour tous les changements de caméra
    cameraMinHoldMs: 4000,                  // 🎯 VALEUR UNIQUE (4 secondes)
    
    // Mapping micros → caméras
    micToCameraMapping: [
        { micName: 'mic_invite1', cameraSource: 'CAM 2' },
        { micName: 'mic_invite2_L', cameraSource: 'CAM 3' },
        { micName: 'mic_invite2_R', cameraSource: 'CAM 3' },
    ],
    
    // Comportements simplifiés
    fallbackBehavior: {
        hideAllWhenInactive: true,          // Masquer quand aucun micro actif
    },
    fullscreenMode: {
        enabled: true,                      // Activer le mode plein écran
        minActiveMics: 2,                   // 2+ micros = plein écran
    },
    
    // Stabilisation audio (paramètres spécialisés)
    audioStabilization: {
        enabled: true,
        fluctuationWindowMs: 3000,          // Fenêtre détection fluctuations
        minStableDurationMs: 1000,          // Durée minimum stable
    },
    
    // Debug
    debug: {
        verbose: false,
        logAudioLevels: false,
        logAudioThresholds: true,
        audioLogIntervalMs: 2000,           // Intervalle logs audio
    },
};
```

## 🎯 **Avantages de cette simplification**

1. **Une seule valeur à ajuster** : `cameraMinHoldMs` contrôle tous les délais
2. **Plus simple à comprendre** : Logique unifiée et cohérente
3. **Moins de confusion** : Fini les multiples paramètres similaires
4. **Plus facile à maintenir** : Moins de paramètres = moins de complexité
5. **Comportement prévisible** : Tous les changements suivent la même règle

## 🔧 **Ajustement unique**

Pour modifier le comportement global du système :
- **Plus réactif** : `cameraMinHoldMs: 3000` (3 secondes)
- **Plus stable** : `cameraMinHoldMs: 5000` (5 secondes)
- **Équilibré** : `cameraMinHoldMs: 4000` (4 secondes) ← **Recommandé**

## ✅ **Résultat**

**Configuration ultra-simplifiée** avec seulement **4 paramètres de timing** au lieu de 6, tout en conservant toute la fonctionnalité et en éliminant la confusion des multiples timeouts similaires.

Le système est maintenant **plus simple**, **plus cohérent** et **plus facile à configurer** !
