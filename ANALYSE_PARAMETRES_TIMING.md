# Analyse des paramètres de timing - Audio to Camera

## 📊 Paramètres actuels et leurs rôles

### 🎤 **Paramètres audio de base**
```typescript
audioThresholdDb: -40,              // Seuil de déclenchement des micros
activationDelayMs: 100,             // <PERSON><PERSON>lai avant activation d'une caméra
```

### 🎥 **Paramètres de changement de caméra**
```typescript
holdDurationMs: 4000,               // Maintien minimum d'une caméra active
minChangeIntervalMs: 3000,          // Délai minimum entre changements
```

### 🖥️ **Paramètres de mode plein écran**
```typescript
multiMicDurationMs: 3000,           // Délai avant d'entrer en plein écran
minHoldDurationMs: 2000,            // Maintien minimum du plein écran
```

### 💤 **Paramètres de fallback**
```typescript
hideDelayMs: 3000,                  // Délai avant masquage si aucun micro actif
```

## ⚠️ **Redondances identifiées**

### 1. **holdDurationMs vs minHoldDurationMs**
- **holdDurationMs (4000ms)** : Maintien minimum d'une caméra normale
- **minHoldDurationMs (2000ms)** : Maintien minimum du mode plein écran
- **Problème** : Logique similaire, noms confus

### 2. **holdDurationMs vs minChangeIntervalMs**
- **holdDurationMs (4000ms)** : "Cette caméra doit rester active 4s minimum"
- **minChangeIntervalMs (3000ms)** : "Attendre 3s minimum entre changements"
- **Problème** : Overlap dans la protection anti-changements rapides

## 🎯 **Proposition de simplification**

### Option A : Paramètres unifiés
```typescript
// Paramètres simplifiés
audioThresholdDb: -40,
activationDelayMs: 100,
minCameraHoldMs: 4000,              // Maintien minimum caméra normale
minFullscreenHoldMs: 2000,          // Maintien minimum plein écran
fullscreenTriggerDelayMs: 3000,     // Délai avant plein écran
inactivityHideDelayMs: 3000,        // Délai avant masquage
```

### Option B : Paramètres par contexte
```typescript
cameraTransitions: {
    activationDelayMs: 100,         // Délai activation
    minHoldDurationMs: 4000,        // Maintien minimum
},
fullscreenMode: {
    triggerDelayMs: 3000,           // Délai avant activation
    minHoldDurationMs: 2000,        // Maintien minimum
},
fallbackBehavior: {
    hideDelayMs: 3000,              // Délai avant masquage
}
```

## 🔍 **Analyse détaillée des interactions**

### Scénario 1 : Changement de caméra normal
1. Micro activé → `activationDelayMs` (100ms) → Caméra activée
2. Caméra maintenue pendant `holdDurationMs` (4000ms) minimum
3. Nouveau micro → Vérification `minChangeIntervalMs` (3000ms)

**Problème** : Si `holdDurationMs` > `minChangeIntervalMs`, le `minChangeIntervalMs` ne sert à rien !

### Scénario 2 : Mode plein écran
1. 2+ micros actifs → `multiMicDurationMs` (3000ms) → Plein écran
2. Plein écran maintenu pendant `minHoldDurationMs` (2000ms) minimum
3. Retour caméra normale

**Cohérent** : Logique claire et séparée

## 💡 **Recommandations**

### 1. **Supprimer la redondance**
Éliminer `minChangeIntervalMs` car `holdDurationMs` fait le même travail de manière plus claire.

### 2. **Renommer pour la clarté**
- `holdDurationMs` → `cameraMinHoldMs`
- `minHoldDurationMs` → `fullscreenMinHoldMs`
- `multiMicDurationMs` → `fullscreenTriggerDelayMs`

### 3. **Configuration simplifiée proposée**
```typescript
export const audioToCameraCustomConfig: AudioToCameraConfig = {
    enabled: true,
    targetScene: 'AUTO CAM',
    audioThresholdDb: -40,
    
    // Transitions de caméra
    activationDelayMs: 100,         // Délai avant activation
    cameraMinHoldMs: 4000,          // Maintien minimum caméra (remplace holdDurationMs + minChangeIntervalMs)
    
    // Mode plein écran
    fullscreenMode: {
        enabled: true,
        triggerDelayMs: 3000,       // Délai avant activation (ex: multiMicDurationMs)
        minHoldMs: 2000,            // Maintien minimum (ex: minHoldDurationMs)
        minActiveMics: 2,
    },
    
    // Comportement inactif
    fallbackBehavior: {
        hideAllWhenInactive: true,
        hideDelayMs: 3000,
    },
    
    // Debug...
};
```

## ❓ **Questions pour vous**

1. **Voulez-vous que je simplifie** la configuration en supprimant les redondances ?
2. **Préférez-vous** garder la flexibilité actuelle ou simplifier ?
3. **Les valeurs actuelles** vous conviennent-elles ou faut-il les ajuster ?
