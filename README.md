# 🎥 OBS Video Hub

**Hub de communication centralisé pour OBS Studio, Gabin et Companion**

OBS Video Hub est une application Node.js/NestJS qui fait le pont entre différents outils de production vidéo :
- **OBS Studio** : Logiciel de streaming/enregistrement ([OBS Studio](https://obsproject.com/))
- **Companion** : Contrôleur de surfaces de contrôle ([Bitfocus Companion](https://bitfocus.io/companion))
- **Gabin** : Système de caméras automatiques ([Fork SUN](https://gitlab.lesonunique.com/vleveque/gabin-fork) de [one-click-studio/gabin](https://github.com/one-click-studio/gabin))

## 🚀 Démarrage Rapide

```bash
# 1. Installation
npm install

# 2. Premier démarrage (génère les configurations)
npm run start

# 3. Activez les modules souhaités dans src/config/custom/modules.custom.ts
# 4. Configurez OBS dans src/config/custom/obs.custom.ts

# 5. Démarrage normal
npm run start:dev
```

> 💡 **Conseil :** Commencez par activer uniquement le module OBS pour tester la connexion

## 🏗️ Architecture

L'application utilise une **architecture modulaire autonome** avec un Hub central d'orchestration :

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│     OBS     │    │ Companion   │    │   Gabin     │
│   Module    │    │   Module    │    │   Module    │
│             │    │             │    │             │
│ • Audio     │    │ • Actions   │    │ • Autocam   │
│ • Caméras   │    │ • Feedbacks │    │ • Micros    │
│ • WebSocket │    │ • OSC       │    │ • OSC       │
└─────────────┘    └─────────────┘    └─────────────┘
       │                   │                   │
       └───────────────────┼───────────────────┘
                           │
                    ┌─────────────┐
                    │ Hub Central │
                    │             │
                    │ • Liens     │
                    │ • Status    │
                    │ • API       │
                    └─────────────┘
```

### 🧩 Modules autonomes
- **OBS** : WebSocket, contrôle audio/vidéo, automation caméras
- **Companion** : OSC, actions/feedbacks pour surfaces de contrôle
- **Gabin** : Communication OSC, gestion autocam/micros
- **Hub** : Orchestration pure, liens entre modules

## 🚀 Installation et Premier Démarrage

### 1. Installation des dépendances

```bash
npm install
```

### 2. Premier démarrage (génération des configurations)

```bash
npm run start
```

**⚠️ Important :** Au premier démarrage, l'application va :
1. Générer automatiquement les fichiers de configuration personnalisés
2. S'arrêter avec un message explicatif
3. Vous demander de personnaliser les configurations si nécessaire

### 3. Configuration des modules

Après le premier démarrage, activez les modules souhaités dans `src/config/custom/modules.custom.ts` :

```typescript
export const modulesCustomConfig: ModulesConfig = {
    gabin: { enabled: false },      // Gabin (optionnel)
    obs: { enabled: true },         // OBS Studio (recommandé)
    companion: { enabled: true },   // Companion (optionnel)
};
```

Puis personnalisez les configurations spécifiques :
- **`src/config/custom/obs.custom.ts`** : Configuration OBS (host, port, password)
- **`src/config/custom/companion.custom.ts`** : Configuration Companion (feedbacks, actions)
- **`src/config/custom/audio-to-camera.custom.ts`** : Automation caméras basée sur l'audio

### 4. Démarrage normal

```bash
# Mode développement
npm run start:dev

# Mode production
npm run start:prod
```

## 🔧 Configuration

### Structure de configuration

L'application utilise un système de configuration personnalisé :

```
src/config/
├── app.config.ts                   # Config générale (HTTP_PORT, NODE_ENV)
├── custom/                         # 🔧 FICHIERS ÉDITABLES (ignorés par Git)
│   ├── modules.custom.ts           # Activation/désactivation des modules
│   ├── obs.custom.ts               # Configuration OBS WebSocket
│   ├── companion.custom.ts         # Configuration Companion OSC
│   ├── gabin.custom.ts             # Configuration Gabin (si activé)
│   └── audio-to-camera.custom.ts   # Automation caméras
├── config.generator.ts             # Génération automatique
└── index.ts                        # Chargement direct
```

### Variables d'environnement (.env)

Créez un fichier `.env` pour la configuration générale :

```bash
# Configuration générale
HTTP_PORT=3000
NODE_ENV=development
```

### Configuration OBS (obs.custom.ts)

```typescript
export const obsCustomConfig: OBSConfig = {
    network: {
        host: '127.0.0.1',
        port: 4455,
        password: 'votre_mot_de_passe',  // Configuré dans OBS
    },
    autoConnect: true,
};
```

### Configuration Automation Caméras (audio-to-camera.custom.ts)

```typescript
export const audioToCameraCustomConfig: AudioToCameraConfig = {
    enabled: true,
    targetScene: 'AUTO CAM',
    audioThresholdDb: -40,              // Seuil de déclenchement des micros
    activationDelayMs: 100,             // Délai d'activation (première activation)
    cameraMinHoldMs: 4000,              // Protection minimum (caméras et plan large)

    micToCameraMapping: [
        { micName: 'mic_invite1', cameraSource: 'CAM 2' },
        { micName: 'mic_invite2', cameraSource: 'CAM 3' },
    ],

    fallbackBehavior: {
        hideAllWhenInactive: true,      // Masquer toutes les caméras quand aucun micro actif
    },

    fullscreenMode: {
        enabled: true,                  // Mode plein écran quand plusieurs caméras actives
        minActiveMics: 2,               // Nombre minimum de caméras pour déclencher
    },

    audioStabilization: {
        enabled: false,                 // Stabilisation contre les fluctuations audio
        fluctuationWindowMs: 2000,
        minStableDurationMs: 500,
    },

    debug: {
        verbose: true,                  // Logs détaillés pour le debug
        logAudioLevels: false,
        logAudioThresholds: true,
        audioLogIntervalMs: 2000,
    },
};
```

## 🌐 API

### Status API

**GET** `http://localhost:3000/status`

Retourne l'état complet du système (modules connectés, liens actifs, etc.)

## 🎛️ Fonctionnalités

### 🎥 Automation Caméras
- **Changement automatique** basé sur les niveaux audio des micros
- **Protection temporelle** : Maintien minimum de 4s pour chaque caméra et plan large
- **Mode plein écran** : Activation automatique quand plusieurs caméras sont actives
- **Stabilisation audio** : Protection contre les fluctuations rapides des micros
- **Configuration flexible** : Mappings micro → caméra personnalisables
- **Debug avancé** : Logs détaillés pour ajuster les seuils et temporisations

### 🎛️ Contrôle Companion
- Actions OSC pour contrôler OBS depuis les surfaces de contrôle
- Feedbacks automatiques vers Companion
- Support des boutons toggle, on/off

### 🔗 Intégration OBS
- Connexion WebSocket vers OBS Studio
- Contrôle des sources audio/vidéo
- Monitoring des niveaux audio en temps réel

## 🐛 Dépannage

### OBS ne se connecte pas
1. Vérifiez qu'OBS Studio est démarré
2. Activez le WebSocket dans OBS : `Outils > obs-websocket Settings`
3. Vérifiez les paramètres dans `obs.custom.ts`

### Companion ne reçoit pas les feedbacks
1. Vérifiez la configuration dans `companion.custom.ts`
2. Envoyez `/companion/ready` depuis Companion
3. Vérifiez que les ports OSC sont corrects

### L'automation caméras ne fonctionne pas
1. **Activez le module** dans `modules.custom.ts`
2. **Vérifiez les noms des sources audio** dans `audio-to-camera.custom.ts`
3. **Ajustez les seuils audio** selon votre environnement (`audioThresholdDb`)
4. **Activez le debug** (`verbose: true`) pour voir les logs détaillés
5. **Vérifiez la scène cible** (`targetScene`) existe dans OBS
6. **Testez les noms des caméras** (`cameraSource`) correspondent aux sources OBS

### Les caméras changent trop rapidement
1. **Augmentez `cameraMinHoldMs`** (ex: 6000ms au lieu de 4000ms)
2. **Activez la stabilisation audio** (`audioStabilization.enabled: true`)
3. **Ajustez le seuil audio** pour éviter les faux déclenchements

### Le plan large ne reste pas assez longtemps
- Le plan large est maintenant protégé pendant `cameraMinHoldMs` comme les caméras
- Augmentez cette valeur si nécessaire (ex: 6000ms)

## 🛠️ Développement

### Scripts disponibles

```bash
# Développement avec rechargement automatique
npm run start:dev

# Build de production
npm run build

# Tests
npm run test

# Linting
npm run lint
```

## 📄 License

MIT License - Voir le fichier [LICENSE](LICENSE) pour plus de détails.
