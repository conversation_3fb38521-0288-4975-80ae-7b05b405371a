# ✅ Correction définitive du plan large

## 🎯 **Problème identifié dans le log**

Dans le log fourni, on voyait :
```
[12:50:23,656] All cameras hidden (fullscreen mode)  // Plan large
[12:50:24,291] Camera activated: CAM 3               // Seulement 635ms plus tard !
```

**Analyse** : Le délai que j'avais ajouté (`fullscreenToSingleDelayMs`) ne s'appliquait que pour la transition **plein écran → caméra**, mais pas pour **plan large → caméra**.

## 🔧 **Solution implémentée**

### **Nouveau paramètre ajouté**
```typescript
fallbackBehavior: {
    hideAllWhenInactive: true,
    hideDelayMs: 3000,                      // Délai avant plan large (inchangé)
    fullscreenToSingleDelayMs: 1500,        // Délai plein écran → caméra (existant)
    inactiveToSingleDelayMs: 1500,          // 🆕 NOUVEAU : D<PERSON><PERSON> plan large → caméra
}
```

### **<PERSON>tat ajouté pour tracker le plan large**
```typescript
interface CameraState {
    // ... propriétés existantes
    isInactive: boolean;                    // 🆕 NOUVEAU : Indique si on est en plan large
}
```

### **Logique de délais améliorée**
```typescript
// Déterminer le délai d'activation selon le contexte
if (this.cameraState.activeCamera === null) {
    if (this.cameraState.isInactive) {
        // On vient du plan large → délai spécifique
        delay = this.config.fallbackBehavior.inactiveToSingleDelayMs;  // 1500ms
    } else {
        // Première activation normale → délai standard
        delay = this.config.activationDelayMs;                        // 100ms
    }
} else {
    // Changement de caméra → immédiat
    delay = 0;
}
```

## 📊 **Matrice des délais complète**

| Transition | Délai | Paramètre | Justification |
|------------|-------|-----------|---------------|
| **Aucun micro → Caméra** | 1500ms | `inactiveToSingleDelayMs` | 🆕 Évite retours rapides du plan large |
| **Plein écran → Caméra** | 1500ms | `fullscreenToSingleDelayMs` | Évite retours rapides du plein écran |
| **Caméra → Caméra** | 4000ms | `cameraMinHoldMs` | Protection anti-changements rapides |
| **Première activation** | 100ms | `activationDelayMs` | Délai minimal pour éviter pics |
| **Micros actifs → Plan large** | 3000ms | `hideDelayMs` | Évite masquage sur pauses courtes |
| **2+ micros → Plein écran** | 3000ms | `triggerDelayMs` | Évite plein écran sur pics courts |

## 🎯 **Résultat attendu**

### **Avant (problématique)**
```
[12:50:23,656] All cameras hidden (plan large)
[12:50:24,291] Camera activated: CAM 3        // 635ms = trop rapide !
```

### **Après (corrigé)**
```
[12:50:23,656] All cameras hidden (plan large)
[12:50:25,156] Camera activated: CAM 3        // 1500ms = délai approprié
```

## 🔍 **Logs améliorés**

Le système affichera maintenant :
```
[14:32:15.123] [AudioToCameraLink] All cameras hidden (plan large)
[14:32:16.623] [AudioToCameraLink] Camera activated: CAM 3 (from inactive state)
```

## ⚙️ **Configuration recommandée finale**

```typescript
export const audioToCameraCustomConfig: AudioToCameraConfig = {
    enabled: true,
    targetScene: 'AUTO CAM',
    audioThresholdDb: -40,
    activationDelayMs: 100,
    cameraMinHoldMs: 4000,
    
    micToCameraMapping: [
        { micName: 'mic_invite1', cameraSource: 'CAM 2' },
        { micName: 'mic_invite2_L', cameraSource: 'CAM 3' },
        { micName: 'mic_invite2_R', cameraSource: 'CAM 3' },
    ],
    
    fallbackBehavior: {
        hideAllWhenInactive: true,
        hideDelayMs: 3000,                      // Délai avant plan large
        fullscreenToSingleDelayMs: 1500,        // Délai plein écran → caméra
        inactiveToSingleDelayMs: 1500,          // 🆕 Délai plan large → caméra
    },
    
    fullscreenMode: {
        enabled: true,
        triggerDelayMs: 3000,
        minActiveMics: 2,
        minHoldMs: 2000,
    },
    
    audioStabilization: {
        enabled: true,
        fluctuationWindowMs: 3000,
        minStableDurationMs: 1000,
    },
    
    debug: {
        verbose: false,
        logAudioLevels: false,
        logAudioThresholds: true,
        audioLogIntervalMs: 2000,
    },
};
```

## 📋 **Test recommandé**

1. **Scénario de test** :
   - Faire parler une personne → caméra activée
   - Arrêter complètement → plan large après 3s
   - Réactiver immédiatement un micro
   - ✅ **Vérifier** : Délai de 1.5s avant retour caméra

2. **Ajustements possibles** :
   - `inactiveToSingleDelayMs: 1000` si 1.5s est trop long
   - `inactiveToSingleDelayMs: 2000` si vous voulez plus de stabilité

## ✅ **Problème résolu**

Le plan large sera maintenant maintenu pendant au moins 1.5 seconde avant de repasser à une caméra, éliminant les basculements rapides et indésirables que vous observiez dans vos tests.
