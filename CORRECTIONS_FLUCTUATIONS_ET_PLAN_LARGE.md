# ✅ Corrections des fluctuations et du plan large

## 🎯 **Problèmes identifiés et corrigés**

### 1. **Pas de délai pour le plan large "par défaut"** ✅ CORRIGÉ
**Problème** : Quand tous les micros se coupaient, le passage au plan large était immédiat, puis la réactivation d'un micro rebasculait instantanément.

**Solution** : Ajout d'un délai `fullscreenToSingleDelayMs` pour les transitions du plein écran vers une caméra unique.

### 2. **Fluctuations rapides empêchent le mode plein écran** ✅ CORRIGÉ
**Problème** : Un micro qui fluctue rapidement (HIGH/LOW/HIGH/LOW...) empêchait la détection de "2 micros stables" pour le plein écran.

**Solution** : Système de stabilisation audio qui ignore les fluctuations rapides.

## 🆕 **Nouvelles fonctionnalités**

### **Stabilisation audio**
```typescript
audioStabilization: {
    enabled: true,                    // Activer la stabilisation
    fluctuationWindowMs: 3000,        // Fenêtre de détection des fluctuations (3s)
    minStableDurationMs: 1000,        // Durée minimum avant changement d'état stable (1s)
}
```

**Fonctionnement** :
- Détecte les micros qui fluctuent (6+ changements en 3 secondes)
- Maintient l'état "stable" pendant au moins 1 seconde
- Utilise l'état stable pour les décisions de caméra

### **Délai de transition plein écran → caméra**
```typescript
fallbackBehavior: {
    hideAllWhenInactive: true,
    hideDelayMs: 3000,                      // Délai avant masquage (inchangé)
    fullscreenToSingleDelayMs: 1500,        // 🆕 NOUVEAU : Délai plein écran → caméra
}
```

## 🔧 **Changements techniques**

### **Interface AudioState étendue**
```typescript
interface AudioState {
    level: 'high' | 'low';              // Niveau brut (fluctue)
    stableLevel: 'high' | 'low';        // 🆕 Niveau stable (filtré)
    lastStableChange: number;           // 🆕 Timestamp dernier changement stable
    recentChanges: number[];            // 🆕 Historique des fluctuations
    // ... autres propriétés existantes
}
```

### **Logique de stabilisation**
1. **Détection des fluctuations** : 6+ changements en 3 secondes = fluctuation
2. **Maintien de l'état stable** : Pendant au moins 1 seconde
3. **Utilisation de l'état stable** : Pour toutes les décisions de caméra

### **Délais améliorés**
- **Plan large → Caméra** : `hideDelayMs` (3000ms) - inchangé
- **Plein écran → Caméra** : `fullscreenToSingleDelayMs` (1500ms) - nouveau
- **Caméra → Caméra** : `cameraMinHoldMs` (4000ms) - inchangé

## 📊 **Configuration recommandée**

```typescript
export const audioToCameraCustomConfig: AudioToCameraConfig = {
    enabled: true,
    targetScene: 'AUTO CAM',
    audioThresholdDb: -40,
    activationDelayMs: 100,
    cameraMinHoldMs: 4000,
    
    micToCameraMapping: [
        { micName: 'mic_invite1', cameraSource: 'CAM 2' },
        { micName: 'mic_invite2_L', cameraSource: 'CAM 3' },
        { micName: 'mic_invite2_R', cameraSource: 'CAM 3' },
    ],
    
    fallbackBehavior: {
        hideAllWhenInactive: true,
        hideDelayMs: 3000,                      // Délai avant plan large
        fullscreenToSingleDelayMs: 1500,        // 🆕 Délai plein écran → caméra
    },
    
    fullscreenMode: {
        enabled: true,
        triggerDelayMs: 3000,                   // Délai avant plein écran
        minActiveMics: 2,
        minHoldMs: 3000,                        // Maintien minimum plein écran
    },
    
    audioStabilization: {                       // 🆕 NOUVEAU
        enabled: true,                          // Activer la stabilisation
        fluctuationWindowMs: 3000,              // Fenêtre de détection (3s)
        minStableDurationMs: 1000,              // Durée minimum stable (1s)
    },
    
    debug: {
        verbose: false,
        logAudioLevels: false,
        logAudioThresholds: true,               // 🔍 Recommandé pour tests
        audioLogIntervalMs: 2000,
    },
};
```

## 🔍 **Logs améliorés**

### **Stabilisation audio**
```
[14:32:15.123] [AudioToCameraLink] mic_invite1 stable level changed to high (stable)
[14:32:16.456] [AudioToCameraLink] mic_invite1 fluctuating, maintaining stable level high (500ms remaining)
[14:32:17.789] [AudioToCameraLink] mic_invite1 stable level changed to low (was fluctuating)
```

### **Transitions avec délais**
```
[14:32:18.123] [AudioToCameraLink] Exiting fullscreen mode (held for 3000ms)
[14:32:19.623] [AudioToCameraLink] Camera activated: CAM 3 in scene AUTO CAM  // +1.5s délai
```

## 🎯 **Résultats attendus**

1. **Fini les retours brefs au plan large** : Délai de 1.5s pour les transitions
2. **Fini les fluctuations qui empêchent le plein écran** : Stabilisation automatique
3. **Comportement plus prévisible** : États stables utilisés pour les décisions
4. **Meilleur debug** : Logs détaillés des fluctuations et stabilisations

## 📋 **Test recommandé**

1. **Tester la stabilisation** :
   - Jouer de la musique avec fluctuations rapides
   - Vérifier que le système détecte un état stable
   - Observer les logs de stabilisation

2. **Tester les délais** :
   - Faire parler 2 personnes → plein écran
   - Arrêter une personne rapidement
   - Vérifier le délai de 1.5s avant retour caméra

3. **Ajuster si nécessaire** :
   - `fluctuationWindowMs` : Fenêtre de détection des fluctuations
   - `minStableDurationMs` : Durée minimum avant changement stable
   - `fullscreenToSingleDelayMs` : Délai plein écran → caméra
