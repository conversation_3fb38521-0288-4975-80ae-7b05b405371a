/**
 * Lien Audio → Caméra
 *
 * Automation intelligente qui contrôle la visibilité des caméras selon les niveaux audio des micros.
 *
 * Fonctionnalités :
 * - Activation quasi-instantanée des caméras (100ms)
 * - Maintien minimum des caméras actives (4s)
 * - Mode plein écran automatique lors de discussions multiples (3s)
 * - Protection contre les changements trop rapides (3s minimum)
 * - Support des micros multiples par caméra
 */

import { IModuleLink, LinkConfig } from '../interfaces/link.interface';
import { IModule, ModuleEvent } from '../interfaces/module.interface';
import { audioToCameraConfig } from '../../config';
import type { MicToCameraMapping } from '../../config';
import { AudioSourceConfig } from '../../obs/obs-observer.service';

interface AudioEventData {
    inputName: string;
    [key: string]: any;
}

interface AudioState {
    /** Niveau audio actuel (high/low) */
    level: 'high' | 'low';
    /** Timestamp de la dernière activation */
    lastActivated: number;
    /** Timestamp de la dernière désactivation */
    lastDeactivated: number;
    /** État stable pour éviter les fluctuations */
    stableLevel: 'high' | 'low';
    /** Timestamp du dernier changement d'état stable */
    lastStableChange: number;
    /** Historique des changements récents pour détecter les fluctuations */
    recentChanges: number[];
}

interface CameraState {
    /** Source caméra actuellement visible */
    activeCamera: string | null;
    /** Timestamp de l'activation de la caméra active */
    activatedAt: number;
    /** Timestamp du dernier changement de caméra */
    lastChangeTime: number;
    /** Mode plein écran actif */
    isFullscreen: boolean;
    /** Timestamp d'entrée en mode plein écran */
    fullscreenActivatedAt: number;
    /** Indique si on est en mode "plan large" (aucune caméra active) */
    isInactive: boolean;
    /** Timer principal pour les actions différées */
    actionTimer: NodeJS.Timeout | null;
    /** Timer pour le mode plein écran */
    fullscreenTimer: NodeJS.Timeout | null;
}

export class AudioToCameraLink implements IModuleLink {
    public readonly name = 'audio-to-camera';
    public readonly description = 'Contrôle la visibilité des caméras selon les niveaux audio des micros';
    public readonly enabled: boolean;

    private obsModule?: IModule;
    private config = audioToCameraConfig();

    // États internes
    private audioStates = new Map<string, AudioState>();
    private cameraState: CameraState = {
        activeCamera: null,
        activatedAt: 0,
        lastChangeTime: 0,
        isFullscreen: false,
        fullscreenActivatedAt: 0,
        isInactive: true,
        actionTimer: null,
        fullscreenTimer: null,
    };

    constructor(private linkConfig: LinkConfig) {
        this.enabled = linkConfig.enabled && this.config?.enabled;
    }

    async initialize(modules: Map<string, IModule>): Promise<void> {
        await Promise.resolve();

        if (!this.enabled) {
            console.log('[AudioToCameraLink] Link disabled - skipping initialization');
            return;
        }

        // Récupérer le module OBS
        this.obsModule = modules.get('obs');
        if (!this.obsModule) {
            console.warn('[AudioToCameraLink] OBS module not found - link will not function');
            return;
        }

        // Initialiser les états audio pour chaque micro configuré
        this.config.micToCameraMapping.forEach((mapping: MicToCameraMapping) => {
            this.audioStates.set(mapping.micName, {
                level: 'low',
                lastActivated: 0,
                lastDeactivated: 0,
                stableLevel: 'low',
                lastStableChange: 0,
                recentChanges: [],
            });
        });

        // Configurer les sources audio à écouter dans l'OBS observer
        this.configureAudioSources();

        // Configurer le debug audio
        this.configureAudioDebug();

        // Écouter les événements audio du module OBS
        this.setupOBSListeners();

        console.log(`[AudioToCameraLink] Initialized with ${this.config.micToCameraMapping.length} mic-to-camera mappings`);
        if (this.config.debug.verbose) {
            console.log('[AudioToCameraLink] Configuration:', {
                targetScene: this.config.targetScene,
                audioThresholdDb: this.config.audioThresholdDb,
                activationDelayMs: this.config.activationDelayMs,
                cameraMinHoldMs: this.config.cameraMinHoldMs,
                mappings: this.config.micToCameraMapping,
            });
        }
    }

    async cleanup(): Promise<void> {
        await Promise.resolve();

        // Nettoyer tous les timers
        this.clearAllTimers();

        console.log('[AudioToCameraLink] Cleaned up');
    }

    // ============================================================================
    // CONFIGURATION DES SOURCES AUDIO
    // ============================================================================

    /**
     * Configure les sources audio à écouter dans l'OBS observer
     */
    private configureAudioSources(): void {
        if (!this.obsModule) return;

        // Créer la liste des sources audio à partir du mapping
        const audioSources: AudioSourceConfig[] = this.config.micToCameraMapping.map((mapping: MicToCameraMapping) => ({
            inputName: mapping.micName,
        }));

        // Configurer les sources dans l'OBS observer via la méthode publique du module
        // eslint-disable-next-line
        (this.obsModule as any).configureAudioSources?.(audioSources);

        if (this.config.debug.verbose) {
            console.log(
                '[AudioToCameraLink] Configured audio sources:',
                audioSources.map((s) => `${s.inputName}`),
            );
        }
    }

    /**
     * Configure le debug audio dans l'OBS observer
     */
    private configureAudioDebug(): void {
        if (!this.obsModule) return;

        const debugConfig = {
            enabled: this.config.debug.logAudioLevels || this.config.debug.logAudioThresholds,
            logThresholds: this.config.debug.logAudioThresholds,
            intervalMs: this.config.debug.audioLogIntervalMs,
        };

        // Configurer le debug dans l'OBS observer via la méthode publique du module
        // eslint-disable-next-line
        (this.obsModule as any).configureAudioDebug?.(debugConfig);

        if (this.config.debug.verbose) {
            console.log('[AudioToCameraLink] Configured audio debug:', debugConfig);
        }
    }

    // ============================================================================
    // GESTION DES ÉVÉNEMENTS AUDIO
    // ============================================================================

    private setupOBSListeners(): void {
        if (!this.obsModule) return;

        const eventCallback = (event: ModuleEvent) => {
            const audioData = event.data as AudioEventData;
            if (event.type === 'input_volume_HIGH' && audioData?.inputName) {
                this.handleMicActivated(audioData.inputName);
            } else if (event.type === 'input_volume_LOW' && audioData?.inputName) {
                this.handleMicDeactivated(audioData.inputName);
            }
        };

        this.obsModule.onEvent(eventCallback);
    }

    private handleMicActivated(micName: string): void {
        // Trouver la caméra associée à ce micro
        const mapping = this.config.micToCameraMapping.find((m: MicToCameraMapping) => m.micName === micName);
        if (!mapping) return;

        const cameraSource = mapping.cameraSource;
        const audioState = this.audioStates.get(micName);
        if (!audioState) return;

        // Mettre à jour l'état audio brut
        audioState.level = 'high';
        audioState.lastActivated = Date.now();

        // Gérer la stabilisation
        const previousStableLevel = audioState.stableLevel;
        this.updateAudioStabilization(micName, 'high');

        // Log si verbose activé et changement d'état stable
        if (this.config.debug.verbose && previousStableLevel !== audioState.stableLevel && audioState.stableLevel === 'high') {
            console.log(`[AudioToCameraLink] 🎤 ${micName} → ${cameraSource} (stable: ${audioState.stableLevel})`);
        }

        // Annuler le timer d'action en cours s'il existe
        if (this.cameraState.actionTimer) {
            clearTimeout(this.cameraState.actionTimer);
            this.cameraState.actionTimer = null;
        }

        // Évaluer la situation avec la nouvelle logique
        this.evaluateCameraState();
    }

    private handleMicDeactivated(micName: string): void {
        // Mettre à jour l'état audio
        const audioState = this.audioStates.get(micName);
        if (audioState) {
            audioState.level = 'low';
            audioState.lastDeactivated = Date.now();

            // Gérer la stabilisation
            const previousStableLevel = audioState.stableLevel;
            this.updateAudioStabilization(micName, 'low');

            // ✅ AMÉLIORATION : Log seulement si changement d'état stable ET que ça va déclencher une action
            if (this.config.debug.verbose && previousStableLevel !== audioState.stableLevel && audioState.stableLevel === 'low') {
                console.log(`[AudioToCameraLink] 🔇 ${micName} désactivé`);
            }
        }

        // Évaluer la situation avec la nouvelle logique
        this.evaluateCameraState();
    }

    // ============================================================================
    // LOGIQUE PRINCIPALE DE DÉCISION
    // ============================================================================

    /**
     * Évaluer l'état des caméras et décider de l'action à prendre - Version simplifiée
     */
    private evaluateCameraState(): void {
        // Grouper les micros actifs par caméra
        const activeCameraGroups = this.getActiveCameraGroups();

        // ✅ AMÉLIORATION : Log seulement si changement significatif
        // (pas à chaque évaluation redondante)

        // Gérer le mode plein écran
        this.handleFullscreenMode(activeCameraGroups);

        // Si on est en mode plein écran, ne pas changer de caméra
        if (this.cameraState.isFullscreen) {
            return;
        }

        const now = Date.now();

        if (activeCameraGroups.size === 0) {
            // Aucun micro actif - programmer le masquage si configuré
            if (this.config.fallbackBehavior.hideAllWhenInactive) {
                this.scheduleAction(
                    () => {
                        void this.hideAllCameras();
                    },
                    this.config.cameraMinHoldMs,
                    'hide_all',
                );
            }
        } else if (activeCameraGroups.size === 1) {
            // Une seule caméra a des micros actifs
            const [cameraSource] = activeCameraGroups.keys();

            // Vérifier si on peut changer de caméra
            const canChange = this.canChangeCamera(cameraSource, now);

            if (canChange.allowed) {
                // ✅ CORRECTION : Délai d'activation selon le contexte
                // - Si aucune caméra active (première activation) → délai minimal
                // - Si sortie du plan large ou changement de caméra → immédiat (protection déjà vérifiée)
                const delay = this.cameraState.activeCamera === null ? this.config.activationDelayMs : 0;

                this.scheduleAction(
                    () => {
                        void this.activateCamera(cameraSource);
                    },
                    delay,
                    `activate_${cameraSource}`,
                );
            } else if (canChange.retryAfter > 0) {
                // Programmer une réévaluation plus tard
                this.scheduleAction(
                    () => {
                        this.evaluateCameraState();
                    },
                    canChange.retryAfter,
                    'reevaluate',
                );
            }
            // ✅ AMÉLIORATION : Supprimer le fallback reevaluate redondant
            // Si retryAfter = 0 et allowed = false, c'est que c'est la même caméra
            // Pas besoin de réévaluer constamment
        }
        // Si activeCameraGroups.size > 1, la logique du mode plein écran s'en occupe
    }

    // ============================================================================
    // CONTRÔLE DES CAMÉRAS
    // ============================================================================

    private async activateCamera(cameraSource: string): Promise<void> {
        if (!this.obsModule) return;

        try {
            const allCameraSources = this.config.micToCameraMapping.map((m: MicToCameraMapping) => m.cameraSource);

            if ((this.config as any).cameraTransition?.useOpacityFilter) {
                // ✅ NOUVELLE MÉTHODE : Utiliser l'opacité pour éviter les frames anciennes
                await this.activateCameraWithOpacity(cameraSource, allCameraSources);
            } else {
                // ✅ MÉTHODE CLASSIQUE : Visibilité normale
                // 1. Activer la nouvelle caméra
                // eslint-disable-next-line
                await (this.obsModule as any).setSourceVisibility(this.config.targetScene, cameraSource, true);

                // 2. Masquer toutes les autres caméras (sauf celle qu'on vient d'activer)
                // eslint-disable-next-line
                await (this.obsModule as any).hideAllSources(this.config.targetScene, allCameraSources, cameraSource);
            }

            // Mettre à jour l'état
            const now = Date.now();
            const wasInPlanLarge = this.cameraState.activeCamera === '__PLAN_LARGE__';
            this.cameraState.activeCamera = cameraSource;
            this.cameraState.activatedAt = now;
            this.cameraState.lastChangeTime = now;
            this.cameraState.isInactive = false;
            this.cameraState.actionTimer = null;

            if (wasInPlanLarge && this.config.debug.verbose) {
                console.log(`[${this.getTimestamp()}] [AudioToCameraLink] Exiting plan large mode to activate ${cameraSource}`);
            }

            console.log(`[${this.getTimestamp()}] [AudioToCameraLink] 📹 ${cameraSource} activée`);
        } catch (error) {
            console.error(`[AudioToCameraLink] Failed to activate camera ${cameraSource}:`, error);
        }
    }

    /**
     * Activer une caméra en utilisant l'opacité pour éviter les frames anciennes
     */
    private async activateCameraWithOpacity(cameraSource: string, allCameraSources: string[]): Promise<void> {
        if (!this.obsModule) return;

        const filterName = (this.config as any).cameraTransition?.filterName || 'Opacity';

        try {
            // 1. S'assurer que toutes les caméras ont un filtre d'opacité
            await this.ensureOpacityFilters(allCameraSources, filterName);

            // 2. Rendre toutes les caméras visibles mais transparentes (mode silencieux car on utilise l'opacité)
            for (const camera of allCameraSources) {
                // eslint-disable-next-line
                await (this.obsModule as any).setSourceVisibility(this.config.targetScene, camera, true, true);
            }

            // 3. Mettre toutes les caméras à opacité 0 (sauf celle à activer)
            const otherCameras = allCameraSources.filter((cam) => cam !== cameraSource);
            for (const camera of otherCameras) {
                // eslint-disable-next-line
                await (this.obsModule as any).setSourceOpacity(camera, 0, filterName);
            }

            // 4. Activer la caméra cible avec opacité 1 (100%)
            // eslint-disable-next-line
            await (this.obsModule as any).setSourceOpacity(cameraSource, 1, filterName);
        } catch (error) {
            console.error('[AudioToCameraLink] Failed to activate camera with opacity:', error);
            // Fallback vers la méthode classique
            // eslint-disable-next-line
            await (this.obsModule as any).setSourceVisibility(this.config.targetScene, cameraSource, true);
            // eslint-disable-next-line
            await (this.obsModule as any).hideAllSources(this.config.targetScene, allCameraSources, cameraSource);
        }
    }

    /**
     * S'assurer que toutes les caméras ont un filtre d'opacité
     */
    private async ensureOpacityFilters(cameraSources: string[], filterName: string): Promise<void> {
        if (!this.obsModule) return;

        for (const camera of cameraSources) {
            try {
                // eslint-disable-next-line
                const hasFilter = await (this.obsModule as any).hasSourceFilter(camera, filterName);
                if (!hasFilter) {
                    // eslint-disable-next-line
                    await (this.obsModule as any).createOpacityFilter(camera, filterName);
                    console.log(`[AudioToCameraLink] Created opacity filter for ${camera}`);
                }
            } catch (error) {
                console.warn(`[AudioToCameraLink] Could not ensure opacity filter for ${camera}:`, error);
            }
        }
    }

    private async hideAllCameras(): Promise<void> {
        if (!this.obsModule) return;

        try {
            const allCameraSources = this.config.micToCameraMapping.map((m: MicToCameraMapping) => m.cameraSource);

            if ((this.config as any).cameraTransition?.useOpacityFilter) {
                // ✅ MÉTHODE OPACITÉ : Mettre toutes les caméras à opacité 0
                const filterName = (this.config as any).cameraTransition?.filterName || 'Opacity';
                await this.ensureOpacityFilters(allCameraSources, filterName);

                for (const camera of allCameraSources) {
                    // eslint-disable-next-line
                    await (this.obsModule as any).setSourceOpacity(camera, 0, filterName);
                }
            } else {
                // ✅ MÉTHODE CLASSIQUE : Masquer les sources
                // eslint-disable-next-line
                await (this.obsModule as any).hideAllSources(this.config.targetScene, allCameraSources);
            }

            // ✅ CORRECTION : Traiter le plan large comme une "caméra" avec protection
            const now = Date.now();
            this.cameraState.lastChangeTime = now;
            this.cameraState.isInactive = true;
            this.cameraState.actionTimer = null;

            // Garder une référence à la caméra précédente pour la protection
            // mais marquer qu'on est en plan large avec un identifiant spécial
            this.cameraState.activeCamera = '__PLAN_LARGE__';
            this.cameraState.activatedAt = now; // ✅ Démarrer la protection du plan large

            console.log(`[${this.getTimestamp()}] [AudioToCameraLink] 📺 Plan large activé`);
        } catch (error) {
            console.error('[AudioToCameraLink] Failed to hide cameras:', error);
        }
    }

    // ============================================================================
    // LOGIQUE SIMPLIFIÉE DE TIMING
    // ============================================================================

    /**
     * Vérifier si on peut changer de caméra selon les contraintes de timing
     */
    private canChangeCamera(targetCamera: string, now: number): { allowed: boolean; retryAfter: number } {
        // Si c'est la même caméra, pas besoin de changer
        if (this.cameraState.activeCamera === targetCamera) {
            return { allowed: false, retryAfter: 0 };
        }

        // ✅ CORRECTION : Si aucune caméra active (null), autoriser immédiatement (première activation)
        if (this.cameraState.activeCamera === null) {
            return { allowed: true, retryAfter: 0 };
        }

        // ✅ CORRECTION : Vérifier la durée de maintien minimum (y compris pour le plan large)
        if (now - this.cameraState.activatedAt < this.config.cameraMinHoldMs) {
            const remainingHold = this.config.cameraMinHoldMs - (now - this.cameraState.activatedAt);
            const currentState = this.cameraState.activeCamera === '__PLAN_LARGE__' ? 'plan large' : `camera ${this.cameraState.activeCamera}`;

            // ✅ AMÉLIORATION : Log seulement si protection significative (> 1s restant)
            if (this.config.debug.verbose && remainingHold > 1000) {
                console.log(`[AudioToCameraLink] ⏳ ${currentState} protégé encore ${Math.round(remainingHold / 1000)}s`);
            }
            return { allowed: false, retryAfter: remainingHold + 100 };
        }

        return { allowed: true, retryAfter: 0 };
    }

    /**
     * Programmer une action avec un délai - Remplace tous les anciens timers
     */
    private scheduleAction(action: () => void, delayMs: number, actionName: string): void {
        // Annuler l'action précédente s'il y en a une
        if (this.cameraState.actionTimer) {
            clearTimeout(this.cameraState.actionTimer);
            this.cameraState.actionTimer = null;
        }

        // ✅ AMÉLIORATION : Log seulement les actions de caméra (pas les réévaluations)
        const isCameraAction = actionName.includes('activate_') || actionName.includes('hide_all');
        if (this.config.debug.verbose && isCameraAction) {
            console.log(`[AudioToCameraLink] ⏰ ${actionName} dans ${Math.round(delayMs / 1000)}s`);
        }

        // Programmer la nouvelle action
        this.cameraState.actionTimer = setTimeout(() => {
            this.cameraState.actionTimer = null;
            // ✅ AMÉLIORATION : Log seulement les exécutions de caméra
            if (this.config.debug.verbose && isCameraAction) {
                console.log(`[AudioToCameraLink] ▶️ ${actionName}`);
            }
            action();
        }, delayMs);
    }

    // ============================================================================
    // MODE PLEIN ÉCRAN (FULLSCREEN)
    // ============================================================================

    /**
     * Grouper les micros actifs par caméra associée - Utilise les niveaux stables
     */
    private getActiveCameraGroups(): Map<string, string[]> {
        const cameraGroups = new Map<string, string[]>();

        // Parcourir tous les micros actifs (utiliser le niveau stable)
        for (const [micName, audioState] of this.audioStates.entries()) {
            if (audioState.stableLevel === 'high') {
                // Trouver la caméra associée à ce micro
                const mapping = this.config.micToCameraMapping.find((m: MicToCameraMapping) => m.micName === micName);
                if (mapping) {
                    const cameraSource = mapping.cameraSource;
                    if (!cameraGroups.has(cameraSource)) {
                        cameraGroups.set(cameraSource, []);
                    }
                    cameraGroups.get(cameraSource)!.push(micName);
                }
            }
        }

        return cameraGroups;
    }

    /**
     * Vérifier si on doit passer en mode plein écran
     */
    private shouldEnterFullscreen(activeCameraGroups: Map<string, string[]>): boolean {
        if (!this.config.fullscreenMode.enabled) {
            return false;
        }

        // Compter le nombre de caméras différentes qui ont des micros actifs
        const activeCameraCount = activeCameraGroups.size;

        return activeCameraCount >= this.config.fullscreenMode.minActiveMics;
    }

    /**
     * Gérer le mode plein écran - Version avec maintien minimum
     */
    private handleFullscreenMode(activeCameraGroups: Map<string, string[]>): void {
        const shouldBeFullscreen = this.shouldEnterFullscreen(activeCameraGroups);
        const now = Date.now();

        if (shouldBeFullscreen && !this.cameraState.isFullscreen) {
            // Démarrer le timer pour le mode plein écran si pas déjà démarré
            if (!this.cameraState.fullscreenTimer) {
                if (this.config.debug.verbose) {
                    console.log(`[${this.getTimestamp()}] [AudioToCameraLink] Multiple cameras active, starting fullscreen timer (${this.config.cameraMinHoldMs}ms)`);
                }

                this.cameraState.fullscreenTimer = setTimeout(() => {
                    // Vérifier à nouveau si on devrait toujours être en plein écran
                    const currentActiveCameraGroups = this.getActiveCameraGroups();
                    if (this.shouldEnterFullscreen(currentActiveCameraGroups)) {
                        if (this.config.debug.verbose) {
                            console.log(`[${this.getTimestamp()}] [AudioToCameraLink] Fullscreen timer expired, entering fullscreen mode`);
                        }
                        this.enterFullscreenMode();
                    } else {
                        if (this.config.debug.verbose) {
                            console.log(`[${this.getTimestamp()}] [AudioToCameraLink] Fullscreen timer expired but conditions no longer met, canceling`);
                        }
                        this.cameraState.fullscreenTimer = null;
                    }
                }, this.config.cameraMinHoldMs);
            }
        } else if (!shouldBeFullscreen) {
            // Annuler le timer de plein écran si les conditions ne sont plus remplies
            if (this.cameraState.fullscreenTimer) {
                clearTimeout(this.cameraState.fullscreenTimer);
                this.cameraState.fullscreenTimer = null;
                if (this.config.debug.verbose) {
                    console.log(`[${this.getTimestamp()}] [AudioToCameraLink] Fullscreen timer canceled (conditions no longer met)`);
                }
            }

            // Sortir du mode plein écran SEULEMENT si le délai minimum est écoulé
            if (this.cameraState.isFullscreen) {
                const timeSinceFullscreenStart = now - this.cameraState.fullscreenActivatedAt;

                if (timeSinceFullscreenStart >= this.config.cameraMinHoldMs) {
                    if (this.config.debug.verbose) {
                        console.log(`[${this.getTimestamp()}] [AudioToCameraLink] Multiple cameras no longer active, exiting fullscreen mode (held for ${timeSinceFullscreenStart}ms)`);
                    }
                    this.exitFullscreenMode(activeCameraGroups);
                } else {
                    const remainingHoldTime = this.config.cameraMinHoldMs - timeSinceFullscreenStart;
                    if (this.config.debug.verbose) {
                        console.log(`[${this.getTimestamp()}] [AudioToCameraLink] Fullscreen mode must be held for ${remainingHoldTime}ms more`);
                    }

                    // Programmer une réévaluation après le délai minimum
                    this.scheduleAction(
                        () => {
                            this.evaluateCameraState();
                        },
                        remainingHoldTime + 100,
                        'reevaluate_fullscreen_exit',
                    );
                }
            }
        }
    }

    /**
     * Entrer en mode plein écran
     */
    private enterFullscreenMode(): void {
        if (this.cameraState.isFullscreen) return;

        const now = Date.now();
        this.cameraState.isFullscreen = true;
        this.cameraState.fullscreenActivatedAt = now;
        this.cameraState.fullscreenTimer = null; // Timer terminé

        if (this.config.debug.verbose) {
            console.log(`[${this.getTimestamp()}] [AudioToCameraLink] Entering fullscreen mode (multiple cameras active)`);
        }

        // Masquer toutes les caméras pour révéler CAM1
        void this.hideAllCameras();
    }

    /**
     * Sortir du mode plein écran
     */
    private exitFullscreenMode(activeCameraGroups: Map<string, string[]>): void {
        if (!this.cameraState.isFullscreen) return;

        const holdDuration = Date.now() - this.cameraState.fullscreenActivatedAt;
        this.cameraState.isFullscreen = false;
        this.cameraState.fullscreenActivatedAt = 0;

        if (this.config.debug.verbose) {
            console.log(`[${this.getTimestamp()}] [AudioToCameraLink] Exiting fullscreen mode (held for ${holdDuration}ms)`);
        }

        // Activer la caméra appropriée selon les micros encore actifs avec délai
        if (activeCameraGroups.size === 1) {
            const [cameraSource] = activeCameraGroups.keys();

            // Ajouter un délai pour éviter les transitions trop rapides du plein écran vers une caméra
            this.scheduleAction(
                () => {
                    void this.activateCamera(cameraSource);
                },
                this.config.cameraMinHoldMs,
                `fullscreen_to_${cameraSource}`,
            );
        }
    }

    // ============================================================================
    // UTILITAIRES
    // ============================================================================

    /**
     * Créer un timestamp formaté pour les logs
     */
    private getTimestamp(): string {
        const now = new Date();
        return now.toLocaleTimeString('fr-FR', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            fractionalSecondDigits: 3,
        });
    }

    // ============================================================================
    // STABILISATION AUDIO
    // ============================================================================

    /**
     * Mettre à jour la stabilisation audio pour éviter les fluctuations rapides
     */
    private updateAudioStabilization(micName: string, newLevel: 'high' | 'low'): void {
        const audioState = this.audioStates.get(micName);
        if (!audioState || !this.config.audioStabilization.enabled) {
            // Si la stabilisation est désactivée, utiliser directement le niveau brut
            if (audioState) {
                audioState.stableLevel = newLevel;
                audioState.lastStableChange = Date.now();
            }
            return;
        }

        const now = Date.now();

        // ✅ CORRECTION CRITIQUE : Si le niveau brut est 'low', forcer immédiatement l'état stable à 'low'
        // Cela évite que la stabilisation maintienne 'high' quand le micro est vraiment silencieux
        if (newLevel === 'low') {
            if (audioState.stableLevel !== 'low') {
                audioState.stableLevel = 'low';
                audioState.lastStableChange = now;
                audioState.recentChanges = []; // Reset l'historique
                if (this.config.debug.verbose) {
                    console.log(`[AudioToCameraLink] 🔇 ${micName} désactivé (forcé)`);
                }
            }
            return;
        }

        // Pour les activations ('high'), on garde la logique de stabilisation
        // Ajouter ce changement à l'historique
        audioState.recentChanges.push(now);

        // Nettoyer l'historique (garder seulement les changements récents)
        const windowStart = now - this.config.audioStabilization.fluctuationWindowMs;
        audioState.recentChanges = audioState.recentChanges.filter((timestamp) => timestamp >= windowStart);

        // Détecter si le micro fluctue trop rapidement
        const isFluctuating = audioState.recentChanges.length >= 6; // 6+ changements dans la fenêtre = fluctuation

        if (isFluctuating) {
            // Micro qui fluctue : maintenir l'état stable actuel pendant un délai minimum
            const timeSinceLastStableChange = now - audioState.lastStableChange;

            if (timeSinceLastStableChange >= this.config.audioStabilization.minStableDurationMs) {
                // Assez de temps écoulé, on peut changer l'état stable
                if (audioState.stableLevel !== newLevel) {
                    audioState.stableLevel = newLevel;
                    audioState.lastStableChange = now;
                    if (this.config.debug.verbose) {
                        console.log(`[AudioToCameraLink] 🎤 ${micName} activé (stabilisé)`);
                    }
                }
            } else if (this.config.debug.verbose) {
                const remaining = this.config.audioStabilization.minStableDurationMs - timeSinceLastStableChange;
                console.log(`[AudioToCameraLink] ${micName} fluctue, attente ${Math.round(remaining)}ms`);
            }
        } else {
            // Micro stable : mettre à jour immédiatement
            if (audioState.stableLevel !== newLevel) {
                audioState.stableLevel = newLevel;
                audioState.lastStableChange = now;
                if (this.config.debug.verbose) {
                    console.log(`[AudioToCameraLink] 🎤 ${micName} activé`);
                }
            }
        }
    }

    /**
     * Nettoyer tous les timers actifs - Version simplifiée
     */
    private clearAllTimers(): void {
        if (this.cameraState.actionTimer) {
            clearTimeout(this.cameraState.actionTimer);
            this.cameraState.actionTimer = null;
        }
        if (this.cameraState.fullscreenTimer) {
            clearTimeout(this.cameraState.fullscreenTimer);
            this.cameraState.fullscreenTimer = null;
        }
    }
}
