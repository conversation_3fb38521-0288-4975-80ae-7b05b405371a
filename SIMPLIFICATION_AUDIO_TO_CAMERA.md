# Simplification du système Audio-to-Camera

## Problèmes identifiés et corrigés

### 1. **Gestion complexe des timers** ✅ CORRIGÉ
**Avant :** 5 timers différents qui pouvaient entrer en conflit :
- `activationTimer` (100ms)
- `hideTimer` (3000ms) 
- `fullscreenTimer` (3000ms)
- `holdPeriodTimer` (4000ms)
- `minChangeIntervalTimer` (3000ms)

**Après :** 2 timers seulement :
- `actionTimer` : Timer unique pour toutes les actions différées
- `fullscreenTimer` : Timer spécifique au mode plein écran

### 2. **Mode plein écran problématique** ✅ CORRIGÉ
**Avant :** Délai de 500ms hardcodé qui causait des retours brefs au plan large
**Après :** Logique simplifiée sans délai artificiel, basculement immédiat

### 3. **Logique de timing complexe** ✅ SIMPLIFIÉ
**Avant :** Multiples fonctions de réévaluation qui se chevauchaient
**Après :** 
- Fonction `canChangeCamera()` centralisée pour vérifier les contraintes
- Fonction `scheduleAction()` unique pour programmer les actions

## Nouvelles fonctionnalités de debug

### Configuration étendue
Nouvelles options dans `audio-to-camera.custom.ts` :
```typescript
debug: {
    verbose: false,                    // Logs détaillés du système
    logAudioLevels: false,            // Logs périodiques des niveaux audio
    logAudioThresholds: true,         // Logs des franchissements de seuils
    audioLogIntervalMs: 2000,         // Intervalle des logs audio (2s)
}
```

### Logs de debug audio
Quand `logAudioThresholds: true` :
- 🎤 Logs périodiques des niveaux : `[obs-observer] 🎤 mic_invite1: -45.2dBFS | -43.0dBu | -25.2VU | Linear: 0.017234 | Seuil: -40dB | État: low → LOW`
- 🔊 Activation : `[obs-observer] 🔊 mic_invite1 ACTIVATED: -38.5dB > -40dB`
- 🔇 Désactivation : `[obs-observer] 🔇 mic_invite1 DEACTIVATED: -42.1dB <= -40dB`

## Comment tester les seuils audio

1. **Activer le debug** dans votre configuration :
```typescript
debug: {
    logAudioThresholds: true,
    audioLogIntervalMs: 1000,  // Log chaque seconde pour plus de détails
}
```

2. **Observer les logs** pendant vos tests :
   - Parlez normalement dans chaque micro
   - Notez les valeurs dBFS affichées
   - Identifiez le niveau habituel de votre voix
   - Vérifiez si le seuil actuel (-40dB) est approprié

3. **Ajuster le seuil** si nécessaire :
```typescript
audioThresholdDb: -35,  // Plus sensible (se déclenche plus facilement)
// ou
audioThresholdDb: -45,  // Moins sensible (nécessite plus de volume)
```

## Changements dans la logique

### Fonction `evaluateCameraState()` simplifiée
- Suppression des multiples vérifications de timing redondantes
- Logique centralisée dans `canChangeCamera()`
- Une seule fonction `scheduleAction()` pour toutes les actions différées

### Gestion du mode plein écran améliorée
- Suppression du délai de 500ms problématique
- Basculement immédiat quand les conditions changent
- Annulation propre du timer quand les conditions ne sont plus remplies

### Avantages de la simplification
1. **Moins de bugs** : Moins de timers = moins de conflits possibles
2. **Plus prévisible** : Logique linéaire plus facile à comprendre
3. **Meilleur debug** : Logs détaillés pour identifier les problèmes
4. **Performance** : Moins d'opérations simultanées

## ✅ NOUVEAU : Correction du mode plein écran

### Problème identifié
Le mode plein écran disparaissait trop rapidement quand une personne arrêtait de parler, même brièvement, causant des basculements indésirables.

### Solution implémentée
**Délai de maintien minimum du plein écran** :
```typescript
fullscreenMode: {
    enabled: true,
    multiMicDurationMs: 3000,        // Délai avant d'entrer en plein écran
    minActiveMics: 2,                // Nombre minimum de micros actifs
    minHoldDurationMs: 2000,         // 🆕 NOUVEAU : Maintien minimum du plein écran
}
```

### Améliorations des logs
- **Timestamps précis** : `[14:32:15.123]` dans tous les logs importants
- **Durées de maintien** : Affichage du temps passé en mode plein écran
- **Logs détaillés** : Raisons des transitions et délais restants

### Exemple de logs améliorés
```
[14:32:15.123] [AudioToCameraLink] Multiple cameras active, starting fullscreen timer (3000ms)
[14:32:18.125] [AudioToCameraLink] Entering fullscreen mode (multiple cameras active)
[14:32:19.200] [AudioToCameraLink] Fullscreen mode must be held for 800ms more
[14:32:20.105] [AudioToCameraLink] Exiting fullscreen mode (held for 2000ms)
```

## Test recommandé

1. **Démarrer l'application** avec debug activé
2. **Tester le mode plein écran** :
   - Faire parler 2 personnes simultanément pendant 3+ secondes
   - Arrêter une personne rapidement après l'activation du plein écran
   - Vérifier que le plein écran reste affiché pendant au moins 2 secondes
3. **Observer les logs** pour vérifier :
   - Les niveaux audio de chaque micro
   - Les transitions entre caméras
   - Les durées de maintien du plein écran
   - L'absence de basculements rapides indésirables
4. **Ajuster les paramètres** selon les observations :
   - `audioThresholdDb` : Sensibilité des micros
   - `minHoldDurationMs` : Durée minimum du plein écran (recommandé: 1500-3000ms)
