/**
 * Classe de base pour tous les modules
 * Fournit les fonctionnalités communes (événements, état de connexion, etc.)
 */

import { IModule, ModuleConnectionStatus, ModuleEvent, ModuleEventCallback, MODULE_EVENTS } from '../interfaces/module.interface';

export abstract class BaseModule implements IModule {
    protected eventCallbacks: Array<ModuleEventCallback> = [];
    protected connectionStatus: ModuleConnectionStatus = {
        connected: false,
        lastSeen: undefined,
        error: undefined,
    };
    protected moduleState: Record<string, any> = {};

    constructor(
        public readonly name: string,
        public readonly enabled: boolean,
    ) {}

    /**
     * Obtenir l'état de connexion
     */
    getConnectionStatus(): ModuleConnectionStatus {
        return { ...this.connectionStatus };
    }

    /**
     * S'abonner aux événements du module
     */
    onEvent<T = any>(callback: ModuleEventCallback<T>): void {
        this.eventCallbacks.push(callback);
    }

    /**
     * Obtenir l'état complet du module
     */
    getState(): Record<string, any> {
        return {
            ...this.moduleState,
            connection: this.getConnectionStatus(),
            enabled: this.enabled,
        };
    }

    /**
     * Méthodes abstraites à implémenter par chaque module
     */
    abstract start(): Promise<void>;
    abstract stop(): Promise<void>;

    /**
     * Émettre un événement vers tous les listeners
     */
    protected emitEvent<T = any>(type: string, data: T): void {
        const event: ModuleEvent<T> = {
            type,
            source: this.name,
            data,
            timestamp: new Date(),
        };

        // ✅ AMÉLIORATION : Filtrer les logs d'événements audio trop verbeux
        const isAudioEvent = type.includes('input_volume_');
        const debugAudioEvents = process.env.DEBUG_AUDIO_EVENTS === 'true';

        if (!isAudioEvent || debugAudioEvents) {
            console.log(`[${this.name}] Event emitted: ${type}`, data);
        }

        this.eventCallbacks.forEach((callback) => {
            try {
                callback(event);
            } catch (error) {
                console.error(`[${this.name}] Error in event callback:`, error);
            }
        });
    }

    /**
     * Mettre à jour l'état de connexion
     */
    protected updateConnectionStatus(status: Partial<ModuleConnectionStatus>): void {
        const oldStatus = this.connectionStatus.connected;

        this.connectionStatus = {
            ...this.connectionStatus,
            ...status,
            lastSeen: status.connected ? new Date() : this.connectionStatus.lastSeen,
        };

        // Émettre un événement si l'état de connexion a changé
        if (oldStatus !== this.connectionStatus.connected) {
            this.emitEvent(MODULE_EVENTS.CONNECTION_CHANGED, {
                connected: this.connectionStatus.connected,
                error: this.connectionStatus.error,
            });
        }
    }

    /**
     * Mettre à jour l'état interne du module
     */
    protected updateState(key: string, value: any): void {
        const oldValue = this.moduleState[key];

        if (oldValue !== value) {
            this.moduleState[key] = value;
            console.log(`[${this.name}] State updated: ${key} = ${value}`);
        }
    }

    /**
     * Vérifier si le module est prêt (connecté et activé)
     */
    protected isReady(): boolean {
        return this.enabled && this.connectionStatus.connected;
    }

    /**
     * Gérer les erreurs du module
     */
    protected handleError(error: Error, context?: string): void {
        const errorMessage = context ? `${context}: ${error.message}` : error.message;

        console.error(`[${this.name}] Error: ${errorMessage}`);

        this.updateConnectionStatus({
            error: errorMessage,
            connected: false,
        });

        this.emitEvent(MODULE_EVENTS.ERROR, {
            message: errorMessage,
            context,
            originalError: error,
        });
    }

    /**
     * Log avec préfixe du module
     */
    protected log(message: string, ...args: any[]): void {
        console.log(`[${this.name}] ${message}`, ...args);
    }
}
