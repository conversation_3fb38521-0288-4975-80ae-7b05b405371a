/**
 * Export centralisé de toutes les configurations
 * Chargement direct des configurations locales
 */

import type { GabinConfig } from '../gabin/gabin.types';
import type { CompanionConfig } from '../companion/companion.types';
import type { OBSConfig } from '../obs/obs.types';

// Types pour les configurations qui n'ont pas de module dédié
export interface ModulesConfig {
    gabin: {
        enabled: boolean;
        description: string;
    };
    obs: {
        enabled: boolean;
        description: string;
    };
    companion: {
        enabled: boolean;
        description: string;
    };
}

export interface MicToCameraMapping {
    micName: string;
    cameraSource: string;
}

export interface AudioToCameraConfig {
    enabled: boolean;
    targetScene: string;
    audioThresholdDb: number;
    activationDelayMs: number;
    cameraMinHoldMs: number;
    micToCameraMapping: MicToCameraMapping[];
    fallbackBehavior: {
        hideAllWhenInactive: boolean;
    };
    fullscreenMode: {
        enabled: boolean;
        minActiveMics: number;
    };
    audioStabilization: {
        enabled: boolean;
        fluctuationWindowMs: number;
        minStableDurationMs: number;
    };
    debug: {
        verbose: boolean;
        logAudioLevels: boolean;
        logAudioThresholds: boolean;
        audioLogIntervalMs: number;
    };
}

// Chargement direct des configurations personnalisées
function loadGabinCustomConfig(): GabinConfig {
    try {
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const customModule = require('./custom/gabin.custom') as { gabinCustomConfig: GabinConfig };
        return customModule.gabinCustomConfig;
    } catch (error) {
        throw new Error(`Failed to load gabin.custom.ts: ${error}`);
    }
}

function loadCompanionCustomConfig(): CompanionConfig {
    try {
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const customModule = require('./custom/companion.custom') as { companionCustomConfig: CompanionConfig };
        return customModule.companionCustomConfig;
    } catch (error) {
        throw new Error(`Failed to load companion.custom.ts: ${error}`);
    }
}

function loadOBSCustomConfig(): OBSConfig {
    try {
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const customModule = require('./custom/obs.custom') as { obsCustomConfig: OBSConfig };
        return customModule.obsCustomConfig;
    } catch (error) {
        throw new Error(`Failed to load obs.custom.ts: ${error}`);
    }
}

function loadModulesCustomConfig(): ModulesConfig {
    try {
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const customModule = require('./custom/modules.custom') as { modulesCustomConfig: ModulesConfig };
        return customModule.modulesCustomConfig;
    } catch (error) {
        throw new Error(`Failed to load modules.custom.ts: ${error}`);
    }
}

function loadAudioToCameraCustomConfig(): AudioToCameraConfig {
    try {
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        const customModule = require('./custom/audio-to-camera.custom') as { audioToCameraCustomConfig: AudioToCameraConfig };
        return customModule.audioToCameraCustomConfig;
    } catch (error) {
        throw new Error(`Failed to load audio-to-camera.custom.ts: ${error}`);
    }
}

// Export des configurations locales (chargement différé)
let _gabinConfig: GabinConfig | undefined;
let _companionConfig: CompanionConfig | undefined;
let _obsConfig: OBSConfig | undefined;
let _modulesConfig: ModulesConfig | undefined;
let _audioToCameraConfig: AudioToCameraConfig | undefined;

export const gabinConfig = (): GabinConfig => {
    if (!_gabinConfig) {
        _gabinConfig = loadGabinCustomConfig();
    }
    return _gabinConfig;
};

export const companionConfig = (): CompanionConfig => {
    if (!_companionConfig) {
        _companionConfig = loadCompanionCustomConfig();
    }
    return _companionConfig;
};

export const obsConfig = (): OBSConfig => {
    if (!_obsConfig) {
        _obsConfig = loadOBSCustomConfig();
    }
    return _obsConfig;
};

export const modulesConfig = (): ModulesConfig => {
    if (!_modulesConfig) {
        _modulesConfig = loadModulesCustomConfig();
    }
    return _modulesConfig;
};

export const audioToCameraConfig = (): AudioToCameraConfig => {
    if (!_audioToCameraConfig) {
        _audioToCameraConfig = loadAudioToCameraCustomConfig();
    }
    return _audioToCameraConfig;
};

// Export de la configuration app (inchangée)
export { appConfig, type AppConfig, type HttpConfig } from './app.config';
